#!/usr/bin/env python3
"""
Test suite for memory backends

Tests the PgVectorBackend and Mem0Backend implementations with comprehensive
validation of memory storage, search, and health monitoring functionality.
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wheel_trader.memory.backends import (
    Mem0Backend,
    MemoryStorageError,
    PgVectorBackend,
    create_memory_backend,
)


class TestPgVectorBackend(unittest.TestCase):
    """Test PgVectorBackend functionality"""
    
    def setUp(self):
        """Set up test environment"""
        # Mock Supabase and OpenAI
        self.mock_supabase = Mock()
        self.mock_openai_client = Mock()

        # Create backend with mocked dependencies
        with patch('wheel_trader.memory.backends.create_client', return_value=self.mock_supabase):
            with patch('wheel_trader.memory.backends.OpenAI', return_value=self.mock_openai_client):
                self.backend = PgVectorBackend()
    
    def test_initialization(self):
        """Test backend initialization"""
        self.assertEqual(self.backend.embedding_model, "text-embedding-3-small")
        self.assertIsNotNone(self.backend.health_stats)
        self.assertEqual(self.backend.health_stats["queries"], 0)
        self.assertEqual(self.backend.health_stats["errors"], 0)
    
    def test_get_embedding(self):
        """Test embedding generation"""
        # Mock OpenAI response with new API structure
        mock_embedding_data = Mock()
        mock_embedding_data.embedding = [0.1, 0.2, 0.3]

        mock_response = Mock()
        mock_response.data = [mock_embedding_data]

        self.mock_openai_client.embeddings.create.return_value = mock_response

        # Test embedding generation
        embedding = self.backend._get_embedding("test content")

        self.assertEqual(embedding, [0.1, 0.2, 0.3])
        self.mock_openai_client.embeddings.create.assert_called_once()
    
    def test_store_memory_success(self):
        """Test successful memory storage"""
        # Mock successful storage
        mock_result = Mock()
        mock_result.data = [{"id": "test-memory-id"}]
        self.mock_supabase.table.return_value.insert.return_value.execute.return_value = mock_result
        
        # Mock embedding generation
        self.mock_openai.Embedding.create.return_value = {
            'data': [{'embedding': [0.1, 0.2, 0.3]}]
        }
        
        # Test memory storage
        memory_id = self.backend.store_memory(
            content="Test memory content",
            metadata={"importance": 0.8, "tags": ["test"]},
            agent_name="test_agent",
            mem_type="test_action"
        )
        
        self.assertEqual(memory_id, "test-memory-id")
        self.assertEqual(self.backend.health_stats["queries"], 1)
        self.assertEqual(self.backend.health_stats["errors"], 0)
    
    def test_store_memory_failure(self):
        """Test memory storage failure handling"""
        # Mock storage failure
        self.mock_supabase.table.return_value.insert.return_value.execute.side_effect = Exception("Storage failed")
        
        # Mock embedding generation
        self.mock_openai.Embedding.create.return_value = {
            'data': [{'embedding': [0.1, 0.2, 0.3]}]
        }
        
        # Test memory storage failure
        with self.assertRaises(MemoryStorageError):
            self.backend.store_memory(
                content="Test memory content",
                metadata={},
                agent_name="test_agent"
            )
        
        self.assertEqual(self.backend.health_stats["errors"], 1)
    
    def test_search_memories_success(self):
        """Test successful memory search"""
        # Mock successful search
        mock_result = Mock()
        mock_result.data = [
            {
                "id": "memory-1",
                "content": "Test memory 1",
                "similarity": 0.9,
                "agent_name": "test_agent",
                "mem_type": "test_action"
            }
        ]
        self.mock_supabase.rpc.return_value.execute.return_value = mock_result
        
        # Mock embedding generation
        self.mock_openai.Embedding.create.return_value = {
            'data': [{'embedding': [0.1, 0.2, 0.3]}]
        }
        
        # Test memory search
        memories = self.backend.search_memories(
            query="test query",
            agent_name="test_agent",
            limit=5
        )
        
        self.assertEqual(len(memories), 1)
        self.assertEqual(memories[0]["id"], "memory-1")
        self.assertEqual(memories[0]["similarity"], 0.9)
    
    def test_search_memories_failure(self):
        """Test memory search failure handling"""
        # Mock search failure
        self.mock_supabase.rpc.return_value.execute.side_effect = Exception("Search failed")
        
        # Mock embedding generation
        self.mock_openai.Embedding.create.return_value = {
            'data': [{'embedding': [0.1, 0.2, 0.3]}]
        }
        
        # Test memory search failure
        with self.assertRaises(MemoryStorageError):
            self.backend.search_memories(query="test query")
    
    def test_health_status(self):
        """Test health status reporting"""
        # Mock database health check
        mock_result = Mock()
        mock_result.data = []
        self.mock_supabase.table.return_value.select.return_value.limit.return_value.execute.return_value = mock_result
        
        # Get health status
        health = self.backend.get_health_status()
        
        self.assertEqual(health["backend_type"], "pgvector")
        self.assertTrue(health["database_connected"])
        self.assertIn("success_rate", health)
        self.assertIn("avg_latency_ms", health)
    
    def test_consolidate_memories(self):
        """Test memory consolidation"""
        # Mock consolidation result
        mock_result = Mock()
        mock_result.data = 5  # 5 memories consolidated
        self.mock_supabase.rpc.return_value.execute.return_value = mock_result
        
        # Test consolidation
        count = self.backend.consolidate_memories(
            agent_name="test_agent",
            similarity_threshold=0.95
        )
        
        self.assertEqual(count, 5)
        self.mock_supabase.rpc.assert_called_with(
            "consolidate_memories",
            {
                "similarity_threshold": 0.95,
                "agent_filter": "test_agent",
                "max_consolidations": 100
            }
        )
    
    def test_cleanup_expired_memories(self):
        """Test expired memory cleanup"""
        # Mock cleanup result
        mock_result = Mock()
        mock_result.data = 3  # 3 memories cleaned up
        self.mock_supabase.rpc.return_value.execute.return_value = mock_result
        
        # Test cleanup
        count = self.backend.cleanup_expired_memories()
        
        self.assertEqual(count, 3)
        self.mock_supabase.rpc.assert_called_with("cleanup_expired_memories")


class TestMem0Backend(unittest.TestCase):
    """Test Mem0Backend functionality"""
    
    def setUp(self):
        """Set up test environment"""
        # Mock mem0 Memory class
        self.mock_memory = Mock()
        
        # Create backend with mocked mem0
        with patch('wheel_trader.memory.backends.Memory', return_value=self.mock_memory):
            self.backend = Mem0Backend()
    
    def test_initialization(self):
        """Test backend initialization"""
        self.assertIsNotNone(self.backend.memory)
        self.assertIsNotNone(self.backend.health_stats)
    
    def test_store_memory_success(self):
        """Test successful memory storage with mem0"""
        # Mock mem0 add response
        self.mock_memory.add.return_value = {"id": "mem0-memory-id"}
        
        # Test memory storage
        memory_id = self.backend.store_memory(
            content="Test memory content",
            metadata={"importance": 0.8},
            agent_name="test_agent",
            mem_type="test_action"
        )
        
        self.assertEqual(memory_id, "mem0-memory-id")
        self.assertEqual(self.backend.health_stats["queries"], 1)
        self.assertEqual(self.backend.health_stats["errors"], 0)
    
    def test_search_memories_success(self):
        """Test successful memory search with mem0"""
        # Mock mem0 search response
        self.mock_memory.search.return_value = {
            "results": [
                {
                    "id": "mem0-memory-1",
                    "memory": "Test memory content",
                    "score": 0.85,
                    "metadata": {"mem_type": "test_action"}
                }
            ]
        }
        
        # Test memory search
        memories = self.backend.search_memories(
            query="test query",
            agent_name="test_agent",
            limit=5
        )
        
        self.assertEqual(len(memories), 1)
        self.assertEqual(memories[0]["id"], "mem0-memory-1")
        self.assertEqual(memories[0]["content"], "Test memory content")
        self.assertEqual(memories[0]["similarity"], 0.85)
    
    def test_health_status(self):
        """Test health status reporting for mem0"""
        # Mock mem0 health check
        self.mock_memory.get_all.return_value = []
        
        # Get health status
        health = self.backend.get_health_status()
        
        self.assertEqual(health["backend_type"], "mem0")
        self.assertTrue(health["mem0_connected"])
        self.assertIn("success_rate", health)


class TestBackendFactory(unittest.TestCase):
    """Test backend factory function"""
    
    def test_create_pgvector_backend(self):
        """Test creating PgVectorBackend"""
        with patch('wheel_trader.memory.backends.create_client'):
            backend = create_memory_backend("pgvector")
            self.assertIsInstance(backend, PgVectorBackend)
    
    def test_create_mem0_backend(self):
        """Test creating Mem0Backend"""
        with patch('wheel_trader.memory.backends.Memory'):
            backend = create_memory_backend("mem0")
            self.assertIsInstance(backend, Mem0Backend)
    
    def test_invalid_backend_type(self):
        """Test invalid backend type"""
        with self.assertRaises(ValueError):
            create_memory_backend("invalid_backend")


class TestMemoryBackendIntegration(unittest.TestCase):
    """Integration tests for memory backends"""
    
    def test_backend_switching(self):
        """Test switching between backends"""
        # Test that we can create different backends
        with patch('wheel_trader.memory.backends.create_client'):
            pgvector_backend = create_memory_backend("pgvector")
            self.assertIsInstance(pgvector_backend, PgVectorBackend)
        
        with patch('wheel_trader.memory.backends.Memory'):
            mem0_backend = create_memory_backend("mem0")
            self.assertIsInstance(mem0_backend, Mem0Backend)
    
    def test_health_stats_tracking(self):
        """Test health statistics tracking across operations"""
        with patch('wheel_trader.memory.backends.create_client'):
            backend = create_memory_backend("pgvector")
            
            # Initial stats should be zero
            self.assertEqual(backend.health_stats["queries"], 0)
            self.assertEqual(backend.health_stats["errors"], 0)
            
            # Health stats should be updated after operations
            backend._update_health_stats(0.1, True)
            self.assertEqual(backend.health_stats["queries"], 1)
            self.assertEqual(backend.health_stats["errors"], 0)
            
            backend._update_health_stats(0.2, False, "Test error")
            self.assertEqual(backend.health_stats["queries"], 2)
            self.assertEqual(backend.health_stats["errors"], 1)
            self.assertEqual(backend.health_stats["last_error"], "Test error")


if __name__ == "__main__":
    print("🧪 Running Memory Backends Test Suite...")
    print("=" * 60)
    
    # Run tests
    unittest.main(verbosity=2)
